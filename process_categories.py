#!/usr/bin/env python3
"""
Script to process category banner images and replace URLs with icon URLs.

This script:
1. Reads categories.csv file
2. Matches banner image files from categories_banner folder
3. Copies images to a new folder with icon URLs as filenames
4. Handles extension differences between .jpg and .jpeg
"""

import csv
import os
import shutil
from pathlib import Path
import re
from urllib.parse import urlparse

def extract_filename_from_url(url):
    """Extract filename from URL path"""
    if not url or url == 'NULL':
        return None
    # Remove 'uploads/categories/banner/' or 'uploads/categories/icon/' prefix
    filename = url.replace('uploads/categories/banner/', '').replace('uploads/categories/icon/', '')
    return filename

def find_matching_image(banner_filename, banner_folder):
    """Find matching image file in banner folder, handling extension differences"""
    if not banner_filename:
        return None
    
    banner_path = Path(banner_folder)
    
    # First try exact match
    exact_match = banner_path / banner_filename
    if exact_match.exists():
        return str(exact_match)
    
    # Try with different extensions
    name_without_ext = Path(banner_filename).stem
    
    # Common image extensions to try
    extensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']
    
    for ext in extensions:
        potential_file = banner_path / f"{name_without_ext}{ext}"
        if potential_file.exists():
            return str(potential_file)
    
    return None

def get_safe_filename(url):
    """Convert URL to safe filename"""
    if not url or url == 'NULL':
        return None
    
    # Extract the actual filename from the URL
    filename = extract_filename_from_url(url)
    if not filename:
        return None
    
    # Replace any unsafe characters
    safe_filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
    return safe_filename

def process_categories():
    """Main function to process categories and copy images"""
    
    # Define paths
    csv_file = 'categories.csv'
    banner_folder = 'categories_banner'
    output_folder = 'processed_categories_with_icon_urls'
    
    # Create output folder
    output_path = Path(output_folder)
    output_path.mkdir(exist_ok=True)
    
    # Statistics
    processed_count = 0
    matched_count = 0
    not_found_count = 0
    no_icon_count = 0
    
    print(f"Processing categories from {csv_file}")
    print(f"Banner images folder: {banner_folder}")
    print(f"Output folder: {output_folder}")
    print("-" * 50)
    
    # Read CSV file
    try:
        with open(csv_file, 'r', encoding='utf-8') as file:
            csv_reader = csv.DictReader(file)
            
            for row in csv_reader:
                processed_count += 1
                
                # Extract banner and icon URLs
                banner_url = row.get('banner', '').strip('"')
                icon_url = row.get('icon', '').strip('"')
                category_name = row.get('name', '').strip('"')
                
                print(f"\nProcessing: {category_name}")
                print(f"Banner URL: {banner_url}")
                print(f"Icon URL: {icon_url}")
                
                # Skip if no icon URL
                if not icon_url or icon_url == 'NULL':
                    print(f"  ❌ No icon URL found for {category_name}")
                    no_icon_count += 1
                    continue
                
                # Extract banner filename
                banner_filename = extract_filename_from_url(banner_url)
                if not banner_filename:
                    print(f"  ❌ Could not extract banner filename from: {banner_url}")
                    not_found_count += 1
                    continue
                
                # Find matching image file
                source_image_path = find_matching_image(banner_filename, banner_folder)
                if not source_image_path:
                    print(f"  ❌ Banner image not found: {banner_filename}")
                    not_found_count += 1
                    continue
                
                # Get safe filename for icon URL
                icon_filename = get_safe_filename(icon_url)
                if not icon_filename:
                    print(f"  ❌ Could not create safe filename from icon URL: {icon_url}")
                    not_found_count += 1
                    continue
                
                # Copy image with new filename
                destination_path = output_path / icon_filename
                
                try:
                    shutil.copy2(source_image_path, destination_path)
                    print(f"  ✅ Copied: {Path(source_image_path).name} → {icon_filename}")
                    matched_count += 1
                    
                except Exception as e:
                    print(f"  ❌ Error copying file: {e}")
                    not_found_count += 1
    
    except FileNotFoundError:
        print(f"❌ Error: {csv_file} not found!")
        return
    except Exception as e:
        print(f"❌ Error reading CSV file: {e}")
        return
    
    # Print summary
    print("\n" + "=" * 50)
    print("PROCESSING SUMMARY")
    print("=" * 50)
    print(f"Total categories processed: {processed_count}")
    print(f"Successfully matched and copied: {matched_count}")
    print(f"Images not found: {not_found_count}")
    print(f"Categories without icon URL: {no_icon_count}")
    print(f"Output folder: {output_folder}")
    
    if matched_count > 0:
        print(f"\n✅ Successfully processed {matched_count} images!")
        print(f"Check the '{output_folder}' folder for results.")
    else:
        print("\n❌ No images were successfully processed.")

if __name__ == "__main__":
    process_categories()
