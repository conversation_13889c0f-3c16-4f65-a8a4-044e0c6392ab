#!/usr/bin/env python3
"""
Script to process sub-category banner images and replace URLs with 250x250 URLs.

This script:
1. Reads files from sub-categories/250X250/JPEG_compressd/ folder (250x250 images)
2. Matches banner image files from sub_categories_banner/ folder
3. Copies banner images to a new folder with 250x250 filenames
4. <PERSON>les extension differences between different formats
"""

import os
import shutil
from pathlib import Path
import re

def extract_base_filename(filename):
    """Extract base filename without extension"""
    return Path(filename).stem

def find_matching_banner_image(target_filename, banner_folder):
    """Find matching banner image file, handling extension differences"""
    if not target_filename:
        return None
    
    banner_path = Path(banner_folder)
    target_base = extract_base_filename(target_filename)
    
    # First try exact match with different extensions
    extensions = ['.png', '.jpg', '.jpeg', '.gif', '.bmp', '.webp']
    
    for ext in extensions:
        potential_file = banner_path / f"{target_base}{ext}"
        if potential_file.exists():
            return str(potential_file)
    
    return None

def get_safe_filename(filename):
    """Convert filename to safe filename, preserving extension"""
    if not filename:
        return None
    
    # Replace any unsafe characters but preserve the structure
    safe_filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
    return safe_filename

def process_sub_categories():
    """Main function to process sub-categories and copy images"""
    
    # Define paths
    source_250x250_folder = 'sub-categories/250X250/JPEG_compressd'
    banner_folder = 'sub_categories_banner'
    output_folder = 'processed_sub_categories_with_250x250_urls'
    
    # Create output folder
    output_path = Path(output_folder)
    output_path.mkdir(exist_ok=True)
    
    # Statistics
    processed_count = 0
    matched_count = 0
    not_found_count = 0
    
    print(f"Processing sub-categories")
    print(f"250x250 images folder: {source_250x250_folder}")
    print(f"Banner images folder: {banner_folder}")
    print(f"Output folder: {output_folder}")
    print("-" * 50)
    
    # Check if source folders exist
    if not Path(source_250x250_folder).exists():
        print(f"❌ Error: {source_250x250_folder} folder not found!")
        return
    
    if not Path(banner_folder).exists():
        print(f"❌ Error: {banner_folder} folder not found!")
        return
    
    # Get all files from 250x250 folder
    source_path = Path(source_250x250_folder)
    
    try:
        # Get all image files from 250x250 folder
        image_extensions = {'.jpeg', '.jpg', '.png', '.gif', '.bmp', '.webp'}
        source_files = [f for f in source_path.iterdir() 
                       if f.is_file() and f.suffix.lower() in image_extensions]
        
        print(f"Found {len(source_files)} files in 250x250 folder")
        print()
        
        for source_file in source_files:
            processed_count += 1
            
            # Extract the base filename (without extension)
            base_filename = source_file.stem
            target_filename = source_file.name  # This will be our target filename
            
            print(f"Processing: {base_filename}")
            print(f"Target filename: {target_filename}")
            
            # Find matching banner image
            banner_image_path = find_matching_banner_image(base_filename, banner_folder)
            if not banner_image_path:
                print(f"  ❌ Banner image not found for: {base_filename}")
                not_found_count += 1
                continue
            
            # Create destination filename (use the 250x250 filename)
            destination_filename = target_filename
            destination_path = output_path / destination_filename
            
            try:
                shutil.copy2(banner_image_path, destination_path)
                print(f"  ✅ Copied: {Path(banner_image_path).name} → {destination_filename}")
                matched_count += 1
                
            except Exception as e:
                print(f"  ❌ Error copying file: {e}")
                not_found_count += 1
    
    except Exception as e:
        print(f"❌ Error reading source folder: {e}")
        return
    
    # Print summary
    print("\n" + "=" * 50)
    print("PROCESSING SUMMARY")
    print("=" * 50)
    print(f"Total files processed: {processed_count}")
    print(f"Successfully matched and copied: {matched_count}")
    print(f"Banner images not found: {not_found_count}")
    print(f"Output folder: {output_folder}")
    
    if matched_count > 0:
        print(f"\n✅ Successfully processed {matched_count} images!")
        print(f"Check the '{output_folder}' folder for results.")
        
        # Show some examples of what was processed
        print(f"\nExamples of processed files:")
        output_files = list(output_path.iterdir())[:5]  # Show first 5 files
        for i, file in enumerate(output_files, 1):
            print(f"  {i}. {file.name}")
        if len(output_files) < matched_count:
            print(f"  ... and {matched_count - len(output_files)} more files")
    else:
        print("\n❌ No images were successfully processed.")

def show_folder_stats():
    """Show statistics about the folders"""
    print("FOLDER STATISTICS")
    print("=" * 50)
    
    # Check 250x250 folder
    source_250x250_folder = 'sub-categories/250X250/JPEG_compressd'
    if Path(source_250x250_folder).exists():
        source_files = list(Path(source_250x250_folder).glob('*'))
        image_files = [f for f in source_files if f.suffix.lower() in {'.jpeg', '.jpg', '.png', '.gif', '.bmp', '.webp'}]
        print(f"250x250 folder: {len(image_files)} image files")
    else:
        print(f"250x250 folder: NOT FOUND")
    
    # Check banner folder
    banner_folder = 'sub_categories_banner'
    if Path(banner_folder).exists():
        banner_files = list(Path(banner_folder).glob('*'))
        banner_images = [f for f in banner_files if f.suffix.lower() in {'.jpeg', '.jpg', '.png', '.gif', '.bmp', '.webp'}]
        print(f"Banner folder: {len(banner_images)} image files")
    else:
        print(f"Banner folder: NOT FOUND")
    
    print()

if __name__ == "__main__":
    show_folder_stats()
    process_sub_categories()
